"""
FastAPI应用主入口
"""
import time
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import <PERSON><PERSON>NResponse
import uvicorn

from app.config import settings
from app.core.exceptions import WebAssistantException
# Removed ResponseHandler - using direct LLM passthrough
from app.utils.logger import get_logger, setup_logging
from app.api.v1 import content, health, default, image_parse
from app.agents.base import agent_registry
from app.agents.content.content_agent import ContentUnderstandingAgent
from app.agents.qa.general_qa_agent import GeneralQAAgent
from app.agents.image.image_parse_agent import ImageParseAgent
from app.core.models import AgentType

# 设置日志
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("正在启动Web Assistant Agents服务...")
    
    # 注册智能体
    agent_registry.register(AgentType.CONTENT_UNDERSTANDING, ContentUnderstandingAgent)
    agent_registry.register(AgentType.GENERAL_QA, GeneralQAAgent)
    agent_registry.register(AgentType.IMAGE_PARSE, ImageParseAgent)
    
    logger.info("智能体注册完成")
    logger.info(f"服务启动完成，监听端口: {settings.port}")
    
    yield
    
    # 关闭时清理
    logger.info("正在关闭Web Assistant Agents服务...")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="基于LangGraph的多智能体浏览器AI助手服务框架",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # 添加中间件
    setup_middleware(app)
    
    # 添加异常处理
    setup_exception_handlers(app)
    
    # 注册路由
    setup_routes(app)
    
    return app


def setup_middleware(app: FastAPI):
    """设置中间件"""
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 信任主机中间件
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", settings.host]
        )
    
    # 请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()
        
        # 记录请求
        logger.info(f"请求开始: {request.method} {request.url}")
    
        
        response = await call_next(request)
        
        # 记录响应
        process_time = time.time() - start_time
        logger.info(f"请求完成: {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
        
        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    @app.exception_handler(WebAssistantException)
    async def web_assistant_exception_handler(request: Request, exc: WebAssistantException):
        """处理自定义异常"""
        logger.error(f"业务异常: {exc.message} - {exc.details}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error_code": exc.error_code,
                "error_message": exc.message,
                "details": exc.details,
                "timestamp": time.time()
            }
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """处理HTTP异常"""
        logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error_code": f"HTTP_{exc.status_code}",
                "error_message": exc.detail,
                "timestamp": time.time()
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """处理通用异常"""
        # 转义特殊字符以避免Rich markup错误
        exc_message = str(exc).replace('[', '\\[').replace(']', '\\]')
        
        # 安全地记录异常信息，避免Rich markup解析错误
        import traceback
        exc_traceback = traceback.format_exc()
        safe_traceback = exc_traceback.replace('[', '\\[').replace(']', '\\]').replace('<', '\\<').replace('>', '\\>')
        logger.error(f"未处理异常: {type(exc).__name__} - {exc_message}")
        logger.error(f"异常堆栈: {safe_traceback}")
        return JSONResponse(
            status_code=500,
            content={
                "error_code": "INTERNAL_SERVER_ERROR",
                "error_message": "服务器内部错误",
                "timestamp": time.time()
            }
        )


def setup_routes(app: FastAPI):
    """设置路由"""
    # API v1路由
    app.include_router(
        health.router,
        prefix="/api/v1",
        tags=["健康检查"]
    )

    app.include_router(
        content.router,
        prefix="/api/v1/content",
        tags=["内容总结"]
    )

    app.include_router(
        default.router,
        prefix="/api/v1",
        tags=["通用问答"]
    )

    app.include_router(
        image_parse.router,
        prefix="/api/v1",
        tags=["图片解析"]
    )
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "service": settings.app_name,
            "version": settings.app_version,
            "status": "running",
            "docs_url": "/docs" if settings.debug else None
        }


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        workers=1 if settings.reload else settings.workers,
        log_level=settings.log_level.lower(),
        access_log=settings.access_log
    )
